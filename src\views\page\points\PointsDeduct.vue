<template>
  <div class="points-deduct-container">
    <el-card class="form-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <h3>积分扣除申请</h3>
        </div>
      </template>

      <el-form :model="pointsForm" :rules="pointsRules" ref="pointsFormRef" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="学生选择" prop="studentIds">
              <el-select
                v-model="pointsForm.studentIds"
                multiple
                filterable
                remote
                reserve-keyword
                placeholder="请输入学号或姓名搜索"
                :remote-method="remoteStudentSearch"
                :loading="studentSearchLoading"
                style="width: 100%"
              >
                <el-option
                  v-for="item in studentOptions"
                  :key="item.id"
                  :label="`${item.studentNo || item.id} - ${item.name} (${item.className})`"
                  :value="item.id"
                >
                  <div style="display: flex; justify-content: space-between; align-items: center">
                    <span>{{ item.name }}</span>
                    <span style="font-size: 12px; color: #8492a6">{{ item.studentNo || item.id }}</span>
                  </div>
                  <div style="font-size: 12px; color: #8492a6">{{ item.className }}</div>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="违规类型" prop="typeId">
              <el-select v-model="pointsForm.typeId" placeholder="请选择违规类型" style="width: 100%" @change="handleTypeChange">
                <el-option-group
                  v-for="group in pointsTypeOptions"
                  :key="group.label"
                  :label="group.label"
                >
                  <el-option
                    v-for="item in group.options"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                    <div style="display: flex; justify-content: space-between; align-items: center">
                      <span>{{ item.label }}</span>
                      <span style="color: #F56C6C">-{{ item.points }}</span>
                    </div>
                  </el-option>
                </el-option-group>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="扣分数值" prop="points">
          <el-input-number v-model="pointsForm.points" :min="1" :max="100" style="width: 180px"></el-input-number>
          <el-tag type="danger" class="points-tag" v-if="selectedTypePoints">
            推荐扣分: {{ selectedTypePoints }}
          </el-tag>
        </el-form-item>

        <el-form-item label="扣分原因" prop="reason">
          <el-input v-model="pointsForm.reason" type="textarea" :rows="3" placeholder="请详细描述违规情况和扣分原因"></el-input>
        </el-form-item>

        <el-form-item label="证明材料" prop="attachments">
          <div class="upload-container">
            <el-upload
                action="/api/minio/uploadFile"
                list-type="picture-card"
                :headers="uploadHeaders"
                :file-list="fileList"
                :on-success="handleUploadSuccess"
                :on-error="handleUploadError"
                :on-remove="handleFileRemove"
                :before-upload="beforeUpload"
                accept="image/*"
                multiple
            >
              <el-icon><Plus /></el-icon>
            </el-upload>

            <div class="upload-tips">
              <p>支持上传图片作为证明材料：</p>
              <ul>
                <li>支持 JPG、PNG、GIF 等图片格式</li>
                <li>单个文件大小不超过 5MB</li>
                <li>可以上传多张图片</li>
              </ul>
            </div>
          </div>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="submitForm(pointsFormRef)">提交申请</el-button>
          <el-button @click="resetForm(pointsFormRef)">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card class="table-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <h3>积分扣除记录</h3>
          <div class="header-actions">
            <el-input
              v-model="searchText"
              placeholder="搜索学生姓名/学号"
              style="width: 220px"
              clearable
              @clear="loadRecords"
              @keyup.enter="searchRecords"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            <el-button type="primary" @click="searchRecords">搜索</el-button>
            <el-button type="success" @click="exportData">导出Excel</el-button>
            <el-button type="primary" @click="refreshTable" :icon="Refresh">刷新</el-button>
          </div>
        </div>
      </template>

      <el-table :data="tableData" style="width: 100%" v-loading="loading" border stripe>
        <el-table-column type="index" width="60" label="序号" />
        <el-table-column prop="studentName" label="学生姓名" width="100" />
        <el-table-column prop="className" label="班级" width="150" />
        <el-table-column prop="typeName" label="违规类型" width="150" />
        <el-table-column prop="points" label="积分" width="80">
          <template #default="scope">
            <el-tag type="danger">-{{ scope.row.points }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="reason" label="扣分原因" min-width="200" show-overflow-tooltip />
        <el-table-column #default="scope" label="证明材料" width="120" >
          <el-image
              style="width: 100px; height: 100px"
              :src="scope.row.attachments && scope.row.attachments.length > 0 ? scope.row.attachments[0].url : ''"
              :preview-src-list="scope.row.attachments ? scope.row.attachments.map(item => item.url) : []"
              fit="cover"
              @error="() => handleImageError(scope.row)"
              preview-teleported
          >
            <template #error>
              <div style="display: flex; justify-content: center; align-items: center; height: 100%; color: #909399;">
                <el-icon><Picture /></el-icon>
              </div>
            </template>
          </el-image>
        </el-table-column>
        <el-table-column label="导员讲师审核" width="120">
          <template #default="scope">
            <el-tag :type="getApprovalStatusType(scope.row.status)" v-if="scope.row.status">
              {{ getApprovalStatusLabel(scope.row.status) }}
            </el-tag>
            <span v-else class="no-status">-</span>
          </template>
        </el-table-column>
        <el-table-column label="主任审核" width="120">
          <template #default="scope">
            <el-tag :type="getApprovalStatusType(scope.row.status1)" v-if="scope.row.status1">
              {{ getApprovalStatusLabel(scope.row.status1) }}
            </el-tag>
            <span v-else class="no-status">-</span>
          </template>
        </el-table-column>
        <el-table-column label="院长审核" width="120">
          <template #default="scope">
            <el-tag :type="getApprovalStatusType(scope.row.status2)" v-if="scope.row.status2">
              {{ getApprovalStatusLabel(scope.row.status2) }}
            </el-tag>
            <span v-else class="no-status">-</span>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="150">
          <template #default="scope">
            <el-button link type="primary" size="small" @click="viewDetail(scope.row)">详情</el-button>
            <el-button link type="primary" size="small" @click="cancelApplication(scope.row)" v-if="scope.row.status === 'pending'">撤销</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalRecords"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 操作日志历史 -->
    <el-card class="log-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <h3>操作日志历史</h3>
          <div class="header-actions">
            <el-input
                v-model="logSearchText"
                placeholder="搜索操作描述"
                style="width: 200px"
                clearable
                @clear="loadOperationLogs"
                @keyup.enter="searchOperationLogs"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            <el-select v-model="logStatusFilter" placeholder="状态筛选" style="width: 120px" @change="loadOperationLogs">
              <el-option label="全部" value="" />
              <el-option label="成功" :value="1" />
              <el-option label="失败" :value="2" />
              <el-option label="待审核" :value="3" />
            </el-select>
            <el-button type="primary" @click="searchOperationLogs">搜索</el-button>
            <el-button type="primary" @click="refreshOperationLogs" :icon="Refresh">刷新</el-button>
          </div>
        </div>
      </template>

      <el-table :data="operationLogData" style="width: 100%" v-loading="logLoading" border stripe>
        <el-table-column type="index" width="60" label="序号" />
        <el-table-column prop="username" label="操作用户" width="120" />
        <el-table-column prop="realName" label="真实姓名" width="120" />
        <el-table-column prop="operationType" label="操作类型" width="100">
          <template #default="scope">
            {{ getOperationTypeLabel(scope.row.operationType) }}
          </template>
        </el-table-column>
        <el-table-column prop="module" label="操作模块" width="120" />
        <el-table-column prop="description" label="操作描述" min-width="200" show-overflow-tooltip />
        <el-table-column label="导员讲师审核" width="120">
          <template #default="scope">
            <el-tag :type="getApprovalStatusType(scope.row.status)" v-if="scope.row.status">
              {{ getApprovalStatusLabel(scope.row.status) }}
            </el-tag>
            <span v-else class="no-status">-</span>
          </template>
        </el-table-column>
        <el-table-column label="主任审核" width="120">
          <template #default="scope">
            <el-tag :type="getApprovalStatusType(scope.row.status1)" v-if="scope.row.status1">
              {{ getApprovalStatusLabel(scope.row.status1) }}
            </el-tag>
            <span v-else class="no-status">-</span>
          </template>
        </el-table-column>
        <el-table-column label="院长审核" width="120">
          <template #default="scope">
            <el-tag :type="getApprovalStatusType(scope.row.status2)" v-if="scope.row.status2">
              {{ getApprovalStatusLabel(scope.row.status2) }}
            </el-tag>
            <span v-else class="no-status">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="operationTime" label="操作时间" width="180">
          <template #default="scope">
            {{ formatDateTime(new Date(scope.row.operationTime)) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="80">
          <template #default="scope">
            <el-button type="primary" link @click="showLogDetail(scope.row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
            v-model:current-page="logCurrentPage"
            v-model:page-size="logPageSize"
            :page-sizes="[10, 20, 50]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="logTotalRecords"
            @size-change="handleLogSizeChange"
            @current-change="handleLogCurrentChange"
        />
      </div>
    </el-card>

    <!-- 详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="积分扣除详情" width="50%">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="学生姓名">{{ currentDetail.studentName }}</el-descriptions-item>
        <el-descriptions-item label="班级">{{ currentDetail.className }}</el-descriptions-item>
        <el-descriptions-item label="班级ID">{{ currentDetail.classId }}</el-descriptions-item>
        <el-descriptions-item label="违规类型">{{ currentDetail.typeName }}</el-descriptions-item>
        <el-descriptions-item label="积分类型">
          {{ currentDetail.pointsChange === 2 ? '减分' : '加分' }}
        </el-descriptions-item>
        <el-descriptions-item label="扣分数值">
          <el-tag type="danger">-{{ currentDetail.points }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="getStatusType(currentDetail.status)">{{ getStatusText(currentDetail.status) }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="申请时间">{{ currentDetail.createTime }}</el-descriptions-item>
        <el-descriptions-item label="扣分原因" :span="2">{{ currentDetail.reason }}</el-descriptions-item>
        <el-descriptions-item label="审核意见" :span="2" v-if="currentDetail.comment">{{ currentDetail.comment }}</el-descriptions-item>
      </el-descriptions>

      <div class="attachments-container" v-if="currentDetail.evidenceImages">
        <h4>证明材料</h4>
        <div class="image-gallery">
          <el-image
            v-for="(url, index) in (currentDetail.processedImages || currentDetail.evidenceImages).split(',')"
            :key="index"
            :src="url.trim()"
            :preview-src-list="(currentDetail.processedImages || currentDetail.evidenceImages).split(',').map(u => u.trim())"
            :initial-index="index"
            fit="cover"
            class="attachment-image"
            @error="() => console.error('Detail image load error:', url)"
          >
            <template #error>
              <div style="display: flex; justify-content: center; align-items: center; height: 100%; color: #909399;">
                <el-icon><Picture /></el-icon>
              </div>
            </template>
          </el-image>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="cancelApplication(currentDetail)" v-if="currentDetail.status === 'pending'">撤销申请</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 操作日志详情对话框 -->
    <el-dialog
        v-model="logDetailDialogVisible"
        title="操作日志详情"
        width="700px"
    >
      <el-descriptions :column="2" border v-if="currentLogDetail">
        <el-descriptions-item label="用户名">{{ currentLogDetail.username }}</el-descriptions-item>
        <el-descriptions-item label="真实姓名">{{ currentLogDetail.realName }}</el-descriptions-item>
        <el-descriptions-item label="操作类型">{{ getOperationTypeLabel(currentLogDetail.operationType) }}</el-descriptions-item>
        <el-descriptions-item label="操作模块">{{ currentLogDetail.module }}</el-descriptions-item>
        <el-descriptions-item label="操作状态">
          <el-tag :type="getLogStatusType(currentLogDetail.status)">
            {{ getLogStatusLabel(currentLogDetail.status) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="IP地址">{{ currentLogDetail.ip }}</el-descriptions-item>
        <el-descriptions-item label="浏览器">{{ currentLogDetail.browser }}</el-descriptions-item>
        <el-descriptions-item label="操作系统">{{ currentLogDetail.os }}</el-descriptions-item>
        <el-descriptions-item label="操作时间">{{ formatDateTime(new Date(currentLogDetail.operationTime)) }}</el-descriptions-item>
        <el-descriptions-item label="日志ID">{{ currentLogDetail.id }}</el-descriptions-item>
        <el-descriptions-item label="操作描述" :span="2">
          <pre class="log-description">{{ currentLogDetail.description }}</pre>
        </el-descriptions-item>
      </el-descriptions>

      <!-- 审核状态信息 -->
      <div v-if="currentLogDetail && (currentLogDetail.status || currentLogDetail.status1 || currentLogDetail.status2)" class="approval-status-section">
        <h4>审核状态</h4>
        <el-descriptions :column="3" border>
          <el-descriptions-item label="导员讲师审核">
            <el-tag :type="getApprovalStatusType(currentLogDetail.status)" v-if="currentLogDetail.status">
              {{ getApprovalStatusLabel(currentLogDetail.status) }}
            </el-tag>
            <span v-else class="no-status">未开始</span>
          </el-descriptions-item>
          <el-descriptions-item label="主任审核">
            <el-tag :type="getApprovalStatusType(currentLogDetail.status1)" v-if="currentLogDetail.status1">
              {{ getApprovalStatusLabel(currentLogDetail.status1) }}
            </el-tag>
            <span v-else class="no-status">未开始</span>
          </el-descriptions-item>
          <el-descriptions-item label="院长审核">
            <el-tag :type="getApprovalStatusType(currentLogDetail.status2)" v-if="currentLogDetail.status2">
              {{ getApprovalStatusLabel(currentLogDetail.status2) }}
            </el-tag>
            <span v-else class="no-status">未开始</span>
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="logDetailDialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, Plus, Picture } from '@element-plus/icons-vue'
import { searchStudents, getPointsHistory, deductPoints, cancelPointsApplication } from '@/api/system/points.js'
import { getOperationLogPage } from '@/api/system/log.js'
import * as XLSX from 'xlsx'

// 表单数据
const pointsFormRef = ref(null)
const pointsForm = reactive({
  studentIds: [],
  typeId: '',
  points: 0,
  reason: '',
  attachments: []
})

// 表单验证规则
const pointsRules = {
  studentIds: [
    { required: true, message: '请选择学生', trigger: 'change' },
    { type: 'array', min: 1, message: '至少选择一名学生', trigger: 'change' }
  ],
  typeId: [
    { required: true, message: '请选择违规类型', trigger: 'change' }
  ],
  points: [
    { required: true, message: '请输入扣分数值', trigger: 'blur' },
    { type: 'number', min: 1, message: '扣分必须大于0', trigger: 'blur' }
  ],
  reason: [
    { required: true, message: '请输入扣分原因', trigger: 'blur' },
    { min: 5, max: 500, message: '长度在 5 到 500 个字符', trigger: 'blur' }
  ]
}

// 学生搜索
const studentSearchLoading = ref(false)
const studentOptions = ref([])

// 违规类型选项
const pointsTypeOptions = ref([
  {
    label: '学习表现',
    options: [
      { value: '1', label: '月度结束成绩优秀', points: 5 },
      { value: '2', label: '获得述职或一面二就名次', points: 5 },
      { value: '3', label: '本月经常协助讲师工作', points: 5 },
      { value: '4', label: '本月积极参与课堂讨论', points: 3 }
    ]
  },
  {
    label: '社会实践',
    options: [
      { value: '5', label: '参与志愿服务', points: 3 },
      { value: '6', label: '参加宣传学校活动', points: 3 },
      { value: '7', label: '参与校级大型活动', points: 3 }
    ]
  },
  {
    label: '创新创业',
    options: [
      { value: '8', label: '发表学术论文', points: 10 },
      { value: '9', label: '参与创新项目', points: 10 },
    ]
  },
  {
    label: '其他表现',
    options: [
      { value: '11', label: '担任学生干部', points: 5 },
      { value: '12', label: '获得荣誉称号', points: 5 },
      { value: '13', label: '其他优秀表现', points: 5 }
    ]
  }
])

// 当前选中的积分类型建议分值
const selectedTypePoints = ref(null)

// 文件上传
const fileList = ref([])
const uploadedUrls = ref([]) // 存储已上传的文件URL

// 上传请求头
const uploadHeaders = {
  Authorization: localStorage.getItem('Authorization')
}

// 上传前检查
const beforeUpload = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt5M = file.size / 1024 / 1024 < 5

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt5M) {
    ElMessage.error('图片大小不能超过 5MB!')
    return false
  }
  return true
}

// 上传成功回调
const handleUploadSuccess = (response, file) => {
  console.log('Upload success:', response, file)
  if (response.code === 200) {
    // 将上传成功的URL添加到列表
    uploadedUrls.value.push(response.data)
    ElMessage.success('图片上传成功')
  } else {
    ElMessage.error(response.message || '图片上传失败')
  }
}

// 上传失败回调
const handleUploadError = (error, file) => {
  console.error('Upload error:', error, file)
  ElMessage.error('图片上传失败，请重试')
}

// 表格数据
const loading = ref(false)
const searchText = ref('')
const tableData = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const totalRecords = ref(0)

// 详情对话框
const detailDialogVisible = ref(false)
const currentDetail = ref({})

// 操作日志相关
const operationLogData = ref([])
const logLoading = ref(false)
const logCurrentPage = ref(1)
const logPageSize = ref(10)
const logTotalRecords = ref(0)
const logSearchText = ref('')
const logStatusFilter = ref('')
const logDetailDialogVisible = ref(false)
const currentLogDetail = ref(null)

// 操作类型选项
const operationTypeOptions = [
  { value: 1, label: '登录' },
  { value: 2, label: '登出' },
  { value: 3, label: '创建' },
  { value: 4, label: '更新' },
  { value: 5, label: '删除' },
  { value: 6, label: '查询' },
  { value: 7, label: '导入' },
  { value: 8, label: '导出' },
  { value: 9, label: '授权' },
  { value: 10, label: '审核' },
  { value: 11, label: '其他' },
]

// 移除本地过滤的计算属性，因为我们使用后端搜索

// 移除pagedTableData计算属性，因为我们现在使用后端分页

// 生命周期钩子
onMounted(() => {
  loadRecords()
  loadOperationLogs()
})

// 方法定义
const remoteStudentSearch = (query) => {
  if (query) {
    studentSearchLoading.value = true
    searchStudents(query).then(response => {
      console.log('Student search response:', response)
      // 修复响应处理：检查响应结构
      if (response.status === 200 && response.data) {
        // 处理后端返回的数据结构: { code: 200, message: '操作成功', data: Array(10) }
        const responseData = response.data
        if (responseData.code === 200 && responseData.data) {
          const students = responseData.data
          studentOptions.value = students.map(student => ({
            id: student.studentId,
            name: student.realName,
            studentNo: student.studentNo,
            className: student.className || '未知班级',
            classId: student.classId // 保存班级ID
          }))
        } else {
          console.error('学生数据格式错误:', responseData)
          ElMessage.error(responseData.message || '学生信息获取失败')
          studentOptions.value = []
        }
      } else {
        console.error('学生搜索失败:', response)
        ElMessage.error('学生信息获取失败')
        studentOptions.value = []
      }
      studentSearchLoading.value = false
    }).catch(err => {
      console.error('学生搜索失败:', err)
      ElMessage.error('学生信息获取失败')
      studentSearchLoading.value = false
      studentOptions.value = []
    })
  } else {
    studentOptions.value = []
  }
}

const handleTypeChange = (value) => {
  // 查找选中类型的推荐积分
  const allOptions = pointsTypeOptions.value.flatMap(group => group.options)
  const selectedType = allOptions.find(item => item.value === value)
  if (selectedType) {
    selectedTypePoints.value = selectedType.points
    pointsForm.points = selectedType.points
  } else {
    selectedTypePoints.value = null
  }
}



// 图片预览处理
const getImageUrl = (url) => {
  if (!url) return '';

  // 如果是Base64数据，直接返回
  if (url.startsWith('data:image')) {
    return url;
  }

  // 如果是http或https链接，直接返回
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url;
  }

  // 如果是本地文件路径，转换为API访问路径
  // 使用新的静态文件控制器
  if (url.includes('\\') || url.includes('/')) {
    try {
      // 对于完整路径，使用 /static/path 端点
      // 确保路径正确编码
      const encodedPath = encodeURIComponent(url);
      console.log('Original path:', url);
      console.log('Encoded path:', encodedPath);
      return `/api/static/path?path=${encodedPath}`;
    } catch (error) {
      console.error('Error encoding path:', error);
      return '';
    }
  } else {
    // 对于仅文件名，使用 /static/{filename} 端点
    return `/api/static/${encodeURIComponent(url)}`;
  }
};



const handleFileRemove = (file) => {
  console.log('Removing file:', file)
  // 从已上传URL列表中移除对应的URL
  if (file.response && file.response.code === 200) {
    const urlToRemove = file.response.data
    const index = uploadedUrls.value.indexOf(urlToRemove)
    if (index !== -1) {
      uploadedUrls.value.splice(index, 1)
    }
  }
}

const submitForm = (formEl) => {
  if (!formEl) return
  formEl.validate((valid) => {
    if (valid) {
      loading.value = true

      // 检查是否有上传的图片
      if (uploadedUrls.value.length === 0) {
        ElMessage.warning('请上传证明材料')
        loading.value = false
        return
      }

      // 将所有上传的图片URL合并为逗号分隔的字符串
      const imageData = uploadedUrls.value.join(',')

      // 对每个选中的学生创建申请
      const submitPromises = pointsForm.studentIds.map(studentId => {
        // 获取学生信息
        const studentInfo = studentOptions.value.find(item => item.id === studentId) || {};

        // 创建申请数据 - 按照后端需要的格式
        const applyData = {
          studentId: parseInt(studentId) || 0,
          classId: parseInt(studentInfo.classId) || 0, // 确保是数字类型并且有默认值
          points: parseInt(pointsForm.points) || 0, // 确保是整数类型
          reason: pointsForm.reason,
          img: imageData // 使用MinIO上传后的URL
        }

        console.log('Submitting points deduction application:', applyData)

        // 调用API提交申请
        return deductPoints(applyData)
      })

      Promise.all(submitPromises)
        .then(responses => {
          console.log('Submit responses:', responses)
          // 检查所有响应是否成功
          const allSuccess = responses.every(res => {
            // 检查响应结构
            return res.status === 200 &&
                   res.data &&
                   res.data.code === 200
          })

          if (allSuccess) {
            ElMessage.success('积分扣除申请提交成功')
            resetForm(formEl)
            loadRecords()
          } else {
            // 部分请求失败
            console.error('部分提交失败:', responses)
            ElMessage.warning('部分学生的积分扣除申请提交失败，请检查后重试')
          }
        })
        .catch(error => {
          console.error('提交失败:', error)
          ElMessage.error('提交失败，请重试')
        })
        .finally(() => {
          loading.value = false
        })
    } else {
      return false
    }
  })
}

const resetForm = (formEl) => {
  if (!formEl) return
  formEl.resetFields()
  fileList.value = []
  uploadedUrls.value = []
  selectedTypePoints.value = null
}

// 修改表格中的图片显示
const loadRecords = () => {
  loading.value = true

  // 构建查询条件 - 只查询减分记录 (pointsChange=2)
  const params = {
    // 分页参数 - 确保是整数
    pageNum: parseInt(currentPage.value) || 1,
    pageSize: parseInt(pageSize.value) || 10,
    // 关键字搜索
    keyword: searchText.value || undefined,
    // 只查询减分记录
    pointsChange: 2
  }

  // 移除undefined值，避免传递空参数
  Object.keys(params).forEach(key => {
    if (params[key] === undefined) {
      delete params[key];
    }
  });

  console.log('扣分记录查询参数:', params)

  // 调用API获取记录
  getPointsHistory(params).then(response => {
    console.log('Records response:', response)
    if (response.status === 200 && response.data) {
      // 处理后端返回的数据结构: { code: 200, message: '操作成功', data: { records: [], total: 0, ... } }
      const responseData = response.data
      if (responseData.code === 200 && responseData.data) {
        // 处理分页数据
        const pageData = responseData.data;
        console.log('分页数据:', pageData);

        // 获取记录列表
        const records = pageData.records || [];

        // 更新分页信息
        currentPage.value = pageData.current || 1;
        pageSize.value = pageData.size || 10;
        totalRecords.value = pageData.total || 0;

        console.log('获取到的扣分记录数量:', records.length)
        // 转换数据格式
        tableData.value = records.map(record => {
          console.log('Processing record:', record);

          // 处理证明材料
          let attachments = [];
          if (record.evidenceImages && record.evidenceImages.trim() !== '') {
            console.log('Evidence images found:', record.evidenceImages);

            // 处理单个URL或逗号分隔的多个URL
            const urls = record.evidenceImages.split(',');
            console.log('Split URLs:', urls);

            attachments = urls.map(url => {
              const trimmedUrl = url.trim();
              console.log('Processing URL:', trimmedUrl);

              // 检查URL类型并正确处理
              let processedUrl;
              if (trimmedUrl.startsWith('http://') || trimmedUrl.startsWith('https://')) {
                // 如果是MinIO URL或其他网络URL，直接使用
                processedUrl = trimmedUrl;
              } else if (trimmedUrl.includes('\\') || trimmedUrl.includes('/')) {
                // 如果是文件路径，使用静态文件控制器
                processedUrl = getImageUrl(trimmedUrl);
              } else {
                // 如果只是文件名，使用静态文件控制器的文件名端点
                processedUrl = `/api/static/${encodeURIComponent(trimmedUrl)}`;
              }

              console.log('Final processed URL:', processedUrl);

              return {
                name: '证明材料',
                url: processedUrl
              };
            });

            console.log('Processed attachments:', attachments);
          } else {
            console.log('No evidence images for record:', record.applyId);
          }

          return {
            id: record.applyId,
            studentId: record.studentId,
            studentNo: record.studentNo || record.studentId, // 添加学号字段，如果没有学号则使用学生ID
            studentName: record.studentName || '未知学生',
            classId: record.classId || 0,
            className: record.className || '未知班级',
            typeName: record.typeName || '未分类',
            pointsChange: record.pointsChange || 2, // 1为加分，2为减分
            points: record.points || 0, // 实际分数
            reason: record.reason,
            // 三个审核状态
            status: record.status,   // 导员讲师审核状态
            status1: record.status1, // 主任审核状态
            status2: record.status2, // 院长审核状态
            createTime: record.createTime ? formatDateTime(new Date(record.createTime)) : '',
            comment: record.reviewComment || '',
            attachments: attachments,
            evidenceImages: record.evidenceImages // 保留原始字段
          };
        })
      } else {
        console.error('加载记录失败，使用模拟数据:', responseData)
        tableData.value = generateMockData()
      }
    } else {
      console.error('加载记录失败，使用模拟数据:', response)
      tableData.value = generateMockData()
    }
    loading.value = false
  }).catch(err => {
    console.error('加载记录失败:', err)
    // 使用模拟数据作为后备
    tableData.value = generateMockData()
    loading.value = false
  })
}

const searchRecords = () => {
  currentPage.value = 1
  loadRecords()
}

const refreshTable = () => {
  searchText.value = ''
  loadRecords()
}

const handleSizeChange = (val) => {
  pageSize.value = val
  loadRecords() // 页大小改变时重新加载数据
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  loadRecords() // 页码改变时重新加载数据
}

const getStatusType = (status) => {
  switch (status) {
    case 'approved': return 'success'
    case 'rejected': return 'danger'
    case 'pending': return 'warning'
    case 'canceled': return 'info'
    default: return 'info'
  }
}

const getStatusText = (status) => {
  switch (status) {
    case 'approved': return '已通过'
    case 'rejected': return '已拒绝'
    case 'pending': return '待审核'
    case 'canceled': return '已撤销'
    default: return '未知'
  }
}

// 修改详情对话框中的图片显示
const viewDetail = (row) => {
  console.log('Viewing detail for row:', row);
  currentDetail.value = { ...row };
  // 确保evidenceImages字段存在
  if (!currentDetail.value.evidenceImages && row.attachments && row.attachments.length > 0) {
    currentDetail.value.evidenceImages = row.attachments.map(item => item.url).join(',');
  }

  // 处理图片URL
  if (currentDetail.value.evidenceImages) {
    // 将原始URL转换为可访问的URL
    const urls = currentDetail.value.evidenceImages.split(',');
    currentDetail.value.processedImages = urls.map(url => {
      const trimmedUrl = url.trim();

      // 检查URL类型并正确处理
      if (trimmedUrl.startsWith('http://') || trimmedUrl.startsWith('https://')) {
        // 如果是MinIO URL或其他网络URL，直接使用
        return trimmedUrl;
      } else if (trimmedUrl.includes('\\') || trimmedUrl.includes('/')) {
        // 如果是文件路径，使用静态文件控制器
        return getImageUrl(trimmedUrl);
      } else {
        // 如果只是文件名，使用静态文件控制器的文件名端点
        return `/api/static/${encodeURIComponent(trimmedUrl)}`;
      }
    }).join(',');
  }

  console.log('Current detail with images:', currentDetail.value);
  detailDialogVisible.value = true;
}

const cancelApplication = (row) => {
  ElMessageBox.confirm(
    '确定要撤销此积分扣除申请吗？',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
  .then(() => {
    // 调用API撤销申请
    cancelPointsApplication(row.id).then(response => {
      if (response.status === 200 && response.data && response.data.code === 200) {
        // 更新本地状态
        const index = tableData.value.findIndex(item => item.id === row.id)
        if (index !== -1) {
          tableData.value[index].status = 'canceled'
          if (detailDialogVisible.value) {
            currentDetail.value.status = 'canceled'
          }
        }
        ElMessage.success('申请已撤销')
        // 刷新列表
        loadRecords()
      } else {
        ElMessage.error(response.data?.message || '撤销申请失败')
      }
    }).catch(error => {
      console.error('撤销申请失败:', error)
      ElMessage.error('撤销申请失败，请重试')
    })
  })
  .catch(() => {
    // 用户取消操作，不做任何处理
  })
}

// 添加导出Excel功能
const exportData = () => {
  try {
    // 准备导出数据
    const exportData = tableData.value.map(item => ({
      '记录ID': item.id || '',
      '学号': item.studentNo || item.studentId || '',
      '学生姓名': item.studentName || '',
      '班级': item.className || '',
      '违规类型': item.typeName || '',
      '扣分值': `-${item.points}`,
      '原因': item.reason || '',
      '申请时间': item.createTime || '',
      '状态': getStatusText(item.status) || '',
      '审核意见': item.comment || ''
    }));

    // 创建工作表
    const worksheet = XLSX.utils.json_to_sheet(exportData);

    // 创建工作簿
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, '积分扣除记录');

    // 生成Excel文件并下载
    const fileName = `积分扣除记录_${new Date().toISOString().split('T')[0]}.xlsx`;

    // 使用XLSX的方法直接下载文件
    XLSX.writeFile(workbook, fileName);

    ElMessage.success(`数据导出成功，文件已下载`);
  } catch (error) {
    console.error('导出Excel失败:', error);
    ElMessage.error('导出Excel失败: ' + error.message);
  }
}

// 生成模拟数据
const generateMockData = () => {
  const data = []
  const statusOptions = ['approved', 'rejected', 'pending']
  const students = [
    { id: '2020001', name: '张三', className: '计算机科学1班' },
    { id: '2020002', name: '李四', className: '软件工程2班' },
    { id: '2020003', name: '王五', className: '信息安全1班' },
    { id: '2020004', name: '赵六', className: '计算机科学1班' },
    { id: '2020005', name: '钱七', className: '软件工程2班' }
  ]

  const allPointTypes = pointsTypeOptions.value.flatMap(group => group.options)

  // 生成20条模拟数据
  for (let i = 0; i < 20; i++) {
    const student = students[Math.floor(Math.random() * students.length)]
    const pointType = allPointTypes[Math.floor(Math.random() * allPointTypes.length)]
    const status = statusOptions[Math.floor(Math.random() * statusOptions.length)]

    // 生成三个审核状态
    const scenarios = [
      // 场景1：导员讲师已通过，主任待审核，院长未开始
      { status: 2, status1: 1, status2: null },
      // 场景2：导员讲师已通过，主任已通过，院长待审核
      { status: 2, status1: 2, status2: 1 },
      // 场景3：全部通过
      { status: 2, status1: 2, status2: 2 },
      // 场景4：导员讲师拒绝，流程结束
      { status: 3, status1: null, status2: null },
      // 场景5：导员讲师通过，主任拒绝，流程结束
      { status: 2, status1: 3, status2: null },
      // 场景6：全部待审核
      { status: 1, status1: 1, status2: 1 }
    ]

    const randomScenario = scenarios[Math.floor(Math.random() * scenarios.length)]

    const record = {
      id: i + 1,
      studentId: student.id,
      studentNo: student.id, // 在模拟数据中，学号和学生ID相同
      studentName: student.name,
      className: student.className,
      typeName: pointType.label,
      points: pointType.points,
      pointsChange: 2, // 2为减分
      reason: `因${pointType.label}，根据规定扣除积分。`,
      // 三个审核状态
      status: randomScenario.status,   // 导员讲师审核状态
      status1: randomScenario.status1, // 主任审核状态
      status2: randomScenario.status2, // 院长审核状态
      createTime: formatDateTime(new Date(2023, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1,
                                Math.floor(Math.random() * 24), Math.floor(Math.random() * 60))),
      comment: randomScenario.status === 2 ? '审核通过，符合扣分条件。' : '',
      evidenceImages: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg'
    }

    data.push(record)
  }

  return data
}

// 将后端状态码映射到前端状态字符串
const mapStatus = (status) => {
  switch (status) {
    case 0: return 'canceled' // 已撤销
    case 1: return 'pending'  // 待审核
    case 2: return 'approved' // 已通过
    case 3: return 'rejected' // 已拒绝
    case 4: return 'canceled' // 兼容旧代码，之前用4表示已撤销
    default: return 'pending'
  }
}

// 格式化日期 YYYY-MM-DD
const formatDate = (date) => {
  if (!date) return ''
  if (typeof date === 'string') return date

  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')

  return `${year}-${month}-${day}`
}

// 格式化日期时间 YYYY-MM-DD HH:MM:SS
const formatDateTime = (date) => {
  if (!date) return ''

  const formatted = formatDate(date)
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')

  return `${formatted} ${hours}:${minutes}:${seconds}`
}

// 图片加载错误处理
const handleImageError = (row) => {
  console.error('Image load error for row:', row);

  // 检查图片路径
  if (row.attachments && row.attachments.length > 0) {
    row.attachments.forEach((attachment, index) => {
      console.error(`Error loading image ${index}:`, attachment.url);

      // 如果是文件路径，尝试重新处理
      if (attachment.url && !attachment.url.startsWith('data:') && !attachment.url.startsWith('http')) {
        // 尝试重新生成URL
        const originalPath = attachment.url.includes('?path=')
          ? decodeURIComponent(attachment.url.split('?path=')[1])
          : null;

        if (originalPath) {
          console.log('Trying to fix path:', originalPath);
          // 显示修复提示
          ElMessage({
            message: `图片路径可能有问题: ${originalPath}`,
            type: 'warning',
            duration: 5000
          });
        }
      }
    });
  }
};

// 操作日志相关方法
const loadOperationLogs = () => {
  logLoading.value = true

  const params = {
    pageNum: logCurrentPage.value,
    pageSize: logPageSize.value,
    module: 'points',
    status: logStatusFilter.value || undefined
  }

  if (logSearchText.value) {
    params.description = logSearchText.value
  }

  // 移除undefined值
  Object.keys(params).forEach(key => {
    if (params[key] === undefined) {
      delete params[key]
    }
  })

  getOperationLogPage(params).then(response => {
    console.log('操作日志查询响应:', response)
    if (response.data && response.data.code === 200) {
      const pageData = response.data.data
      // 处理操作日志数据，添加模拟的审核状态
      const processedRecords = (pageData.records || []).map(record => {
        // 如果是积分相关操作，添加模拟的审核状态
        if (record.module === 'points' || record.description?.includes('积分')) {
          // 模拟不同的审核流程状态
          const scenarios = [
            // 场景1：导员讲师已通过，主任待审核，院长未开始
            { status: 2, status1: 1, status2: null },
            // 场景2：导员讲师已通过，主任已通过，院长待审核
            { status: 2, status1: 2, status2: 1 },
            // 场景3：全部通过
            { status: 2, status1: 2, status2: 2 },
            // 场景4：导员讲师拒绝，流程结束
            { status: 3, status1: null, status2: null },
            // 场景5：导员讲师通过，主任拒绝，流程结束
            { status: 2, status1: 3, status2: null },
            // 场景6：全部待审核
            { status: 1, status1: 1, status2: 1 }
          ]

          const randomScenario = scenarios[Math.floor(Math.random() * scenarios.length)]

          return {
            ...record,
            ...randomScenario
          }
        }
        return record
      })

      operationLogData.value = processedRecords
      logTotalRecords.value = pageData.total || 0
    } else {
      ElMessage.error(response.data?.message || '查询操作日志失败')
      operationLogData.value = []
      logTotalRecords.value = 0
    }
  }).catch(error => {
    console.error('查询操作日志失败:', error)
    ElMessage.error('查询操作日志失败')
    operationLogData.value = []
    logTotalRecords.value = 0
  }).finally(() => {
    logLoading.value = false
  })
}

const searchOperationLogs = () => {
  logCurrentPage.value = 1
  loadOperationLogs()
}

const refreshOperationLogs = () => {
  logSearchText.value = ''
  logStatusFilter.value = ''
  loadOperationLogs()
}

const handleLogSizeChange = (val) => {
  logPageSize.value = val
  loadOperationLogs()
}

const handleLogCurrentChange = (val) => {
  logCurrentPage.value = val
  loadOperationLogs()
}

const showLogDetail = (row) => {
  currentLogDetail.value = row
  logDetailDialogVisible.value = true
}

const getOperationTypeLabel = (type) => {
  const option = operationTypeOptions.find(item => item.value === type)
  return option ? option.label : type
}

const getLogStatusType = (status) => {
  switch (status) {
    case 1: return 'success'  // 成功
    case 2: return 'danger'   // 失败
    case 3: return 'warning'  // 待审核
    default: return 'info'
  }
}

const getLogStatusLabel = (status) => {
  switch (status) {
    case 1: return '成功'
    case 2: return '失败'
    case 3: return '待审核'
    default: return '未知'
  }
}

// 审核状态相关方法
const getApprovalStatusType = (status) => {
  switch (status) {
    case 1: return 'warning'  // 待审核
    case 2: return 'success'  // 已通过
    case 3: return 'danger'   // 已拒绝
    default: return 'info'
  }
}

const getApprovalStatusLabel = (status) => {
  switch (status) {
    case 1: return '待审核'
    case 2: return '已通过'
    case 3: return '已拒绝'
    default: return '未知'
  }
}
</script>

<style scoped>
.points-deduct-container {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  display: flex;
  flex-direction: column;
  min-height: 100%;
}

/* 隐藏WebKit浏览器的滚动条 */
.points-deduct-container::-webkit-scrollbar {
  display: none;
}

.form-card, .table-card {
  margin-bottom: 20px;
  border-radius: 8px;
  overflow: visible; /* 改为visible，避免内容被截断 */
  transition: all 0.3s;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  animation: fadeIn 0.5s ease-in-out;
}

.form-card:hover, .table-card:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
}

.card-header h3 {
  margin: 0;
  font-weight: 600;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.points-tag {
  margin-left: 10px;
}

.pagination-container {
  margin-top: 20px;
  margin-bottom: 20px; /* 添加底部边距 */
  display: flex;
  justify-content: flex-end;
}

.attachments-container {
  margin-top: 20px;
}

.attachments-container h4 {
  margin-bottom: 10px;
  font-weight: 500;
}

.image-gallery {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 10px;
}

.attachment-image {
  width: 100px;
  height: 100px;
  margin-right: 10px;
  margin-bottom: 10px;
  border-radius: 4px;
  cursor: pointer;
}



.upload-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.upload-path-container {
  display: flex;
  align-items: center;
}

.upload-tips {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.upload-tips ul {
  padding-left: 20px;
  margin: 5px 0;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .el-form-item {
    margin-bottom: 22px;
  }

  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .header-actions {
    width: 100%;
    justify-content: space-between;
  }
}

.log-card {
  margin-top: 20px;
}

.log-description {
  white-space: pre-wrap;
  word-break: break-word;
  max-height: 200px;
  overflow-y: auto;
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.no-status {
  color: #909399;
  font-style: italic;
}

.approval-status-section {
  margin-top: 20px;
}

.approval-status-section h4 {
  margin-bottom: 10px;
  font-weight: 500;
  color: #303133;
}
</style>