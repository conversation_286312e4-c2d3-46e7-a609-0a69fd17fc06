package com.zhentao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zhentao.pojo.PointsApply;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 积分变动申请表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
public interface PointsApplyMapper extends BaseMapper<PointsApply> {

    /**
     * 查询积分申请列表，并关联学生和班级信息
     * @param params 查询参数
     * @return 积分申请列表
     */
    @Select({
            "<script>",
            "SELECT pa.*, es.real_name as studentName, ec.class_name as className ",
            "FROM points_apply pa ",
            "LEFT JOIN edu_student es ON pa.student_no = es.student_no ",
            "LEFT JOIN edu_class ec ON es.class_id = ec.class_id ",
            "WHERE 1=1 ",
            "<if test='params == null'>",
            "AND pa.del_flag = 0 ",
            "</if>",
            "<if test='params != null'>",
            "AND pa.del_flag = 0 ",
            "<if test='params.keyword != null and params.keyword != \"\"'>",
            "AND (es.real_name LIKE CONCAT('%', #{params.keyword}, '%') ",
            "OR es.student_no LIKE CONCAT('%', #{params.keyword}, '%') ",
            "OR pa.reason LIKE CONCAT('%', #{params.keyword}, '%')) ",
            "</if>",
            "<if test='params.pointsChange != null'>",
            "AND pa.points_change = #{params.pointsChange} ",
            "</if>",
            "</if>",
            "ORDER BY pa.create_time DESC",
            "</script>"
    })
    List<Map<String, Object>> listPointsAppliesWithStudentInfo(@Param("params") Map<String, Object> params);

    /**
     * 获取积分历史记录，支持多种过滤条件
     * @param params 过滤参数
     * @return 积分历史记录列表
     */
    @Select({
            "<script>",
            "SELECT pa.apply_id as id, pa.student_no as studentNo, es.real_name as studentName, ",
            "ec.class_name as className, ec.class_id as classId, ",
            "pa.points_change as pointsChange, pa.points, pa.reason, pa.evidence_images as evidenceImages, ",
            "pa.status, pa.create_time as createTime, pa.review_time as approveTime, ",
            "pa.review_comment as comment, pa.create_by as operatorId, pa.reviewer_id as approverId ",
            "FROM points_apply pa ",
            "LEFT JOIN edu_student es ON pa.student_no = es.student_no ",
            "LEFT JOIN edu_class ec ON es.class_id = ec.class_id ",
            "WHERE 1=1 ",
            "<if test='params == null'>",
            "AND pa.del_flag = 0 ",
            "</if>",
            "<if test='params != null'>",
            "AND pa.del_flag = 0 ",
            "<if test='params.applyUserId != null and params.applyUserId != \"\"'>",
            "AND pa.apply_user_id = #{params.applyUserId} ",
            "</if>",
            "<if test='params.studentNo != null and params.studentNo != \"\"'>",
            "AND pa.student_no = #{params.studentNo} ",
            "</if>",
            "<if test='params.className != null and params.className != \"\"'>",
            "AND ec.class_name = #{params.className} ",
            "</if>",
            "<if test='params.pointsChange != null'>",
            "AND pa.points_change = #{params.pointsChange} ",
            "</if>",
            "<if test='params.category != null and params.category != \"\"'>",
            "AND pa.reason LIKE CONCAT('%', #{params.category}, '%') ",
            "</if>",
            "<if test='params.status != null'>",
            "AND pa.status = #{params.status} ",
            "</if>",
            "<if test='params.startDate != null and params.startDate != \"\" and params.endDate != null and params.endDate != \"\"'>",
            "AND pa.create_time BETWEEN #{params.startDate} AND #{params.endDate} ",
            "</if>",
            "<if test='params.keyword != null and params.keyword != \"\"'>",
            "AND (es.real_name LIKE CONCAT('%', #{params.keyword}, '%') ",
            "OR es.student_no LIKE CONCAT('%', #{params.keyword}, '%') ",
            "OR pa.reason LIKE CONCAT('%', #{params.keyword}, '%')) ",
            "</if>",
            "</if>",
            "ORDER BY pa.create_time DESC",
            "</script>"
    })
    List<Map<String, Object>> getPointsHistory(@Param("params") Map<String, Object> params);

    /**
     * 获取积分统计信息
     * @param params 过滤参数
     * @return 积分统计信息
     */
    @Select({
            "<script>",
            "SELECT ",
            "IFNULL(SUM(CASE WHEN points_change = 1 AND status = 2 THEN points ELSE 0 END), 0) as totalAddPoints, ",
            "IFNULL(SUM(CASE WHEN points_change = 2 AND status = 2 THEN points ELSE 0 END), 0) as totalDeductPoints, ",
            "COUNT(CASE WHEN status = 1 THEN 1 ELSE NULL END) as pendingCount, ",
            "COUNT(CASE WHEN status = 2 THEN 1 ELSE NULL END) as approvedCount, ",
            "COUNT(CASE WHEN status = 3 THEN 1 ELSE NULL END) as rejectedCount ",
            "FROM points_apply ",
            "WHERE del_flag = 0 ",
            "<if test='params != null and params.studentNo != null and params.studentNo != \"\"'>",
            "AND student_no = #{params.studentNo} ",
            "</if>",
            "<if test='params != null and params.classId != null and params.classId != \"\"'>",
            "AND class_id = #{params.classId} ",
            "</if>",
            "<if test='params != null and params.startDate != null and params.startDate != \"\" and params.endDate != null and params.endDate != \"\"'>",
            "AND create_time BETWEEN #{params.startDate} AND #{params.endDate} ",
            "</if>",
            "</script>"
    })
    Map<String, Object> getPointsStatistics(@Param("params") Map<String, Object> params);

    /**
     * 获取讲师/导员的积分历史记录（只能看到自己班级学生的申请和自己创建的申请）
     * @param params 过滤参数
     * @param teacherId 教师ID
     * @return 积分历史记录列表
     */
    @Select({
            "<script>",
            "SELECT pa.apply_id as id, pa.student_no as studentNo, es.real_name as studentName, ",
            "ec.class_name as className, ec.class_id as classId, ",
            "pa.points_change as pointsChange, pa.points, pa.reason, pa.evidence_images as evidenceImages, ",
            "pa.status, pa.status1, pa.status2, pa.create_time as createTime, pa.review_time as approveTime, ",
            "pa.review_comment as comment, pa.create_by as operatorId, pa.reviewer_id as approverId ",
            "FROM points_apply pa ",
            "LEFT JOIN edu_student es ON pa.student_no = es.student_no ",
            "LEFT JOIN edu_class ec ON es.class_id = ec.class_id ",
            "WHERE pa.del_flag = 0 ",
            "AND (",
            "  ec.teacher_id = #{teacherId} ",  // 自己班级的学生申请
            "  OR pa.apply_user_id = #{teacherId} ", // 自己申请的记录
            ") ",
            "<if test='params != null'>",
            "<if test='params.studentNo != null and params.studentNo != \"\"'>",
            "AND pa.student_no = #{params.studentNo} ",
            "</if>",
            "<if test='params.className != null and params.className != \"\"'>",
            "AND ec.class_name = #{params.className} ",
            "</if>",
            "<if test='params.pointsChange != null'>",
            "AND pa.points_change = #{params.pointsChange} ",
            "</if>",
            "<if test='params.category != null and params.category != \"\"'>",
            "AND pa.reason LIKE CONCAT('%', #{params.category}, '%') ",
            "</if>",
            "<if test='params.status != null'>",
            "AND pa.status = #{params.status} ",
            "</if>",
            "<if test='params.startDate != null and params.startDate != \"\" and params.endDate != null and params.endDate != \"\"'>",
            "AND pa.create_time BETWEEN #{params.startDate} AND #{params.endDate} ",
            "</if>",
            "<if test='params.keyword != null and params.keyword != \"\"'>",
            "AND (es.real_name LIKE CONCAT('%', #{params.keyword}, '%') ",
            "OR es.student_no LIKE CONCAT('%', #{params.keyword}, '%') ",
            "OR pa.reason LIKE CONCAT('%', #{params.keyword}, '%')) ",
            "</if>",
            "</if>",
            "ORDER BY pa.create_time DESC",
            "</script>"
    })
    List<Map<String, Object>> getPointsHistoryForTeacher(@Param("params") Map<String, Object> params, @Param("teacherId") Integer teacherId);

    /**
     * 查询用户自己申请的积分记录（通过apply_user_id字段）
     */
    @Select({
            "SELECT pa.apply_id as id, pa.student_no as studentNo, es.real_name as studentName, ",
            "ec.class_name as className, ec.class_id as classId, ",
            "pa.points_change as pointsChange, pa.points, pa.reason, pa.evidence_images as evidenceImages, ",
            "pa.status, pa.status1, pa.status2, pa.create_time as createTime, pa.review_time as approveTime, ",
            "pa.review_comment as comment, pa.create_by as operatorId, pa.reviewer_id as approverId ",
            "FROM points_apply pa ",
            "LEFT JOIN edu_student es ON pa.student_no = es.student_no ",
            "LEFT JOIN edu_class ec ON es.class_id = ec.class_id ",
            "WHERE pa.del_flag = 0 ",
            "AND pa.apply_user_id = #{applyUserId} ",
            "ORDER BY pa.create_time DESC"
    })
    List<Map<String, Object>> getPointsHistoryByApplyUser(@Param("applyUserId") Integer applyUserId);

}
