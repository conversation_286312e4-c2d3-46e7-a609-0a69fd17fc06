package com.zhentao.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.HashMap;
import java.util.ArrayList;
import com.zhentao.mapper.PointsApplyMapper;
import com.zhentao.mapper.SysUserRoleMapper;
import com.zhentao.pojo.EduClass;
import com.zhentao.pojo.EduStudent;
import com.zhentao.pojo.PointsApply;
import com.zhentao.pojo.SysUser;
import com.zhentao.pojo.SysUserRole;
import com.zhentao.service.EduClassService;
import com.zhentao.service.EduStudentService;
import com.zhentao.service.PointsApplyService;
import com.zhentao.utils.Result;
import com.zhentao.utils.UserContext;
import io.minio.MinioClient;
import io.minio.ObjectWriteResponse;
import io.minio.PutObjectArgs;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 积分变动申请表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@Service
public class PointsApplyServiceImpl extends ServiceImpl<PointsApplyMapper, PointsApply> implements PointsApplyService {

    @Autowired
    private PointsApplyMapper pointsApplyMapper;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private EduStudentService eduStudentService;

    @Autowired
    private EduClassService eduClassService;

    @Autowired
    private SysUserRoleMapper sysUserRoleMapper;

    // MinIO配置，可以考虑移到配置文件中
    private static final String MINIO_ENDPOINT = "http://182.254.244.209:9000";
    private static final String MINIO_ACCESS_KEY = "minioadmin";
    private static final String MINIO_SECRET_KEY = "minioadmin";
    private static final String MINIO_BUCKET = "jifen";

    @Override
    public Result addJiFen(@RequestBody PointsApply pointsApply){
        try {
            // 打印接收到的对象
            System.out.println("接收到的积分申请数据: " + pointsApply);
            System.out.println("积分变动值: " + pointsApply.getPointsChange());
            System.out.println("接收到的积分申请对象: " + pointsApply);

            // 创建minio的客户端
            MinioClient minioClient = MinioClient.builder()
                    .credentials(MINIO_ACCESS_KEY, MINIO_SECRET_KEY)
                    .endpoint(MINIO_ENDPOINT)
                    .build();

            // 处理图片路径
            String imgData = pointsApply.getImg();
            String evidenceUrls = "";

            if (imgData != null && !imgData.isEmpty()) {
                // 检查是否有多个图片（以逗号分隔）
                String[] imgItems = imgData.split(",");
                StringBuilder urlBuilder = new StringBuilder();

                for (String imgItem : imgItems) {
                    try {
                        String fileName;
                        InputStream imageStream;

                        // 检查是否是Base64编码的图片数据
                        if (imgItem.startsWith("data:image")) {
                            // 处理Base64编码的图片
                            int commaIndex = imgItem.indexOf(",");
                            if (commaIndex > 0) {
                                String base64Data = imgItem.substring(commaIndex + 1);
                                byte[] imageBytes = Base64.getDecoder().decode(base64Data);
                                imageStream = new ByteArrayInputStream(imageBytes);

                                // 生成唯一文件名
                                String extension = getImageExtension(imgItem);
                                fileName = UUID.randomUUID().toString() + extension;

                                System.out.println("处理Base64图片数据");
                            } else {
                                System.out.println("无效的Base64图片数据");
                                continue;
                            }
                        } else if (imgItem.startsWith("http://") || imgItem.startsWith("https://")) {
                            // 如果已经是URL，直接添加到结果中
                            if (urlBuilder.length() > 0) {
                                urlBuilder.append(",");
                            }
                            urlBuilder.append(imgItem.trim());
                            System.out.println("添加已有URL: " + imgItem.trim());
                            continue;
                        } else {
                            // 尝试作为文件路径处理
                            String filePath = imgItem.trim();
                            File file = new File(filePath);

                            // 检查文件是否存在
                            if (!file.exists() && !file.isAbsolute()) {
                                // 如果不是绝对路径，尝试从D:/img/image/目录读取（兼容旧代码）
                                filePath = "D:/img/image/" + imgItem.trim();
                                file = new File(filePath);
                            }

                            if (file.exists()) {
                                imageStream = new FileInputStream(file);
                                fileName = file.getName();
                                System.out.println("成功读取文件: " + filePath);
                            } else {
                                System.out.println("文件不存在: " + filePath);
                                continue;
                            }
                        }

                        // 执行上传操作
                        PutObjectArgs putObjectArgs = PutObjectArgs.builder()
                                .bucket(MINIO_BUCKET)
                                .object(fileName)
                                .contentType(getContentType(fileName))
                                .stream(imageStream, imageStream.available(), -1)
                                .build();

                        // 执行上传
                        ObjectWriteResponse response = minioClient.putObject(putObjectArgs);
                        System.out.println("File uploaded: " + response);

                        // 获取地址
                        String path = minioClient.getObjectUrl(putObjectArgs.bucket(), putObjectArgs.object());
                        System.out.println("URL: " + path);

                        // 添加到URL列表
                        if (urlBuilder.length() > 0) {
                            urlBuilder.append(",");
                        }
                        urlBuilder.append(path);

                        // 关闭流
                        imageStream.close();
                    } catch (Exception e) {
                        System.out.println("处理图片时出错: " + imgItem);
                        e.printStackTrace();
                    }
                }

                // 设置证据图片URL
                evidenceUrls = urlBuilder.toString();
            }

            // 如果成功上传了图片，设置URL，否则设置为空
            if (!evidenceUrls.isEmpty()) {
                pointsApply.setEvidenceImages(evidenceUrls);
            } else {
                // 如果没有成功上传图片，设置为空字符串，不保存原始数据（可能过长）
                pointsApply.setEvidenceImages("");
                System.out.println("警告: 没有成功上传图片");
            }

            // 设置其他必要字段
            pointsApply.setApplyUserId(UserContext.getCurrentUser().getUserId());
            pointsApply.setCreateTime(new Date());
            // 确保pointsChange字段有值 (1为加分，2为减分)
            if (pointsApply.getPointsChange() == null) {
                System.out.println("警告: pointsChange字段为空，设置默认值");
                pointsApply.setPointsChange(1); // 默认为加分
            }

            // 如果前端没有传递状态，则根据当前用户角色自动设置审核状态
            if (pointsApply.getStatus() == null && pointsApply.getStatus1() == null && pointsApply.getStatus2() == null) {
                setInitialApprovalStatus(pointsApply);
            } else {
                System.out.println("使用前端传递的审核状态 - 导员讲师: " + pointsApply.getStatus() +
                        ", 主任: " + pointsApply.getStatus1() +
                        ", 院长: " + pointsApply.getStatus2());
            }

            // 确保points字段有值
            if (pointsApply.getPoints() == null) {
                System.out.println("警告: points字段为空，设置默认值");
                pointsApply.setPoints(1); // 设置默认值，避免数据库错误
            }

            // 确保classId字段有值
            if (pointsApply.getClassId() == null) {
                System.out.println("警告: classId字段为空，请检查前端传值");
            }

            // 清除原始img字段，避免数据库插入时包含大量Base64数据
            pointsApply.setImg(null);

            System.out.println("插入数据库前的积分申请对象: " + pointsApply);

            // 插入数据库
            int insert = pointsApplyMapper.insert(pointsApply);
            return insert != 0 ? Result.OK() : Result.ERROR();

        } catch (Exception e) {
            e.printStackTrace();
            return Result.ERROR("添加积分申请失败: " + e.getMessage());
        }
    }

    // 根据文件名获取内容类型
    private String getContentType(String fileName) {
        fileName = fileName.toLowerCase();
        if (fileName.endsWith(".jpg") || fileName.endsWith(".jpeg")) {
            return "image/jpeg";
        } else if (fileName.endsWith(".png")) {
            return "image/png";
        } else if (fileName.endsWith(".gif")) {
            return "image/gif";
        } else if (fileName.endsWith(".bmp")) {
            return "image/bmp";
        } else if (fileName.endsWith(".webp")) {
            return "image/webp";
        } else {
            return "application/octet-stream";
        }
    }

    // 从Base64数据中获取图片扩展名
    private String getImageExtension(String base64Data) {
        if (base64Data.contains("data:image/jpeg") || base64Data.contains("data:image/jpg")) {
            return ".jpg";
        } else if (base64Data.contains("data:image/png")) {
            return ".png";
        } else if (base64Data.contains("data:image/gif")) {
            return ".gif";
        } else if (base64Data.contains("data:image/bmp")) {
            return ".bmp";
        } else if (base64Data.contains("data:image/webp")) {
            return ".webp";
        } else {
            return ".jpg"; // 默认扩展名
        }
    }

    @Override
    public Result listPointsApplies(Map<String, Object> params) {
        try {
            System.out.println("listPointsApplies 原始参数: " + params);

            // 如果参数为空，创建一个空的Map
            if (params == null) {
                params = new HashMap<>();
            }

            // 如果没有指定pointsChange，默认查询所有记录
            if (!params.containsKey("pointsChange")) {
                System.out.println("未指定pointsChange参数，将查询所有记录");
            }

            System.out.println("listPointsApplies 处理后参数: " + params);

            // 直接使用JDBC查询所有记录，用于调试
            String sql = "SELECT * FROM points_apply LIMIT 10";
            List<Map<String, Object>> debugRecords = jdbcTemplate.queryForList(sql);
            System.out.println("直接JDBC查询到 " + debugRecords.size() + " 条记录");

            // 使用Mapper的自定义方法，直接关联查询学生和班级信息
            List<Map<String, Object>> resultList = pointsApplyMapper.listPointsAppliesWithStudentInfo(params);
            System.out.println("listPointsApplies 结果数量: " + (resultList != null ? resultList.size() : 0));

            // 如果结果为空，返回空数组而不是null
            if (resultList == null) {
                resultList = new ArrayList<>();
            }

            return Result.OK(resultList);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.ERROR("获取积分申请列表失败: " + e.getMessage());
        }
    }

    @Override
    public Result getPointsHistory(PointsApply pointsApply) {
        // 添加空值检查，确保分页参数有默认值
        if (pointsApply == null) {
            pointsApply = new PointsApply();
        }

        // 设置默认分页参数
        Integer pageNum = pointsApply.getPageNum();
        Integer pageSize = pointsApply.getPageSize();
        if (pageNum == null || pageNum < 1) {
            pageNum = 1;
        }
        if (pageSize == null || pageSize < 1) {
            pageSize = 10;
        }

        // 获取当前用户信息，实现权限控制
        SysUser currentUser = UserContext.getCurrentUser();
        if (currentUser != null) {
            Integer userId = currentUser.getUserId();

            // 查询用户的角色
            QueryWrapper<SysUserRole> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_id", userId);
            List<SysUserRole> userRoles = sysUserRoleMapper.selectList(queryWrapper);

            List<Integer> roleIds = userRoles.stream()
                    .map(SysUserRole::getRoleId)
                    .collect(Collectors.toList());

            System.out.println("当前用户角色ID列表: " + roleIds);

            // 角色权限控制：按最高权限角色判断
            // 权限从高到低：1 > 2,3 > 4,5 > 6
            if (roleIds.contains(1)) {
                // 超级管理员(1)可以查看所有申请
                System.out.println("用户具有超级管理员角色，可以查看所有申请");
            } else if (roleIds.contains(2) || roleIds.contains(3)) {
                // 专业主任(2)、专病主任(3)可以查看所有申请
                System.out.println("用户具有主任角色，可以查看所有申请");
            } else if (roleIds.contains(4) || roleIds.contains(5)) {
                // 讲师(4)、导员(5)：可以查看自己班级学生的申请 + 自己创建的申请
                System.out.println("用户具有讲师/导员角色，限制只能查看自己班级学生的申请和自己创建的申请，用户ID: " + userId);
                return getPointsHistoryForTeacher(pointsApply, userId);
            } else if (roleIds.contains(6)) {
                // 秘书(6)只能看到自己申请的记录
                System.out.println("用户具有秘书角色，限制只能查看自己申请的记录，用户ID: " + userId);
                return getPointsHistoryForSecretary(pointsApply, userId);
            }
        }

        Page<PointsApply> page = new Page<>(pageNum, pageSize);
        QueryWrapper<PointsApply> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(pointsApply.getApplyUserId() != null, "apply_user_id", pointsApply.getApplyUserId());
        queryWrapper.eq(pointsApply.getStudentNo() != null, "student_no", pointsApply.getStudentNo());
        queryWrapper.eq(pointsApply.getClassId() != null, "class_id", pointsApply.getClassId());
        queryWrapper.eq(pointsApply.getStatus() != null, "status", pointsApply.getStatus());
        queryWrapper.eq(pointsApply.getPointsChange() != null, "points_change", pointsApply.getPointsChange());
        queryWrapper.eq(pointsApply.getCreateBy() != null, "create_by", pointsApply.getCreateBy());
        queryWrapper.eq(pointsApply.getReviewerId() != null, "reviewer_id", pointsApply.getReviewerId());
        queryWrapper.between(pointsApply.getStartTime() != null && pointsApply.getEndTime() != null,
                "create_time", pointsApply.getStartTime(), pointsApply.getEndTime());
        // 添加按创建时间倒序排序
        queryWrapper.orderByDesc("create_time");

        // 添加调试信息
        System.out.println("getPointsHistory 查询条件: " + queryWrapper.getSqlSegment());
        System.out.println("分页参数: pageNum=" + pageNum + ", pageSize=" + pageSize);

        // 查询分页数据
        Page<PointsApply> pageResult = pointsApplyMapper.selectPage(page, queryWrapper);

        System.out.println("getPointsHistory 查询结果: total=" + pageResult.getTotal() + ", records=" + pageResult.getRecords().size());

        // 如果查询成功，填充学生和班级信息
        if (pageResult != null && pageResult.getRecords() != null) {
            for (PointsApply record : pageResult.getRecords()) {
                // 获取学生信息
                if (record.getStudentNo() != null) {
                    QueryWrapper<EduStudent> studentWrapper = new QueryWrapper<>();
                    studentWrapper.eq("student_no", record.getStudentNo());
                    EduStudent student = eduStudentService.getOne(studentWrapper);
                    if (student != null) {
                        record.setStudent(student);
                        // 设置学生姓名和学号
                        record.setStudentName(student.getRealName());
                        record.setStudentNo(student.getStudentNo()); // 添加学号字段
                    }
                }

                // 获取班级信息
                if (record.getClassId() != null) {
                    EduClass eduClass = eduClassService.getById(record.getClassId());
                    if (eduClass != null) {
                        record.setEduClass(eduClass);
                        // 设置班级名称
                        record.setClassName(eduClass.getClassName());
                    }
                }

                // 设置积分类型文本
                if (record.getPointsChange() != null) {
                    record.setType(record.getPointsChange() == 1 ? "add" : "deduct");
                }

                // 设置状态文本
                if (record.getStatus() != null) {
                    switch (record.getStatus()) {
                        case 1:
                            record.setStatusText("pending");
                            break;
                        case 2:
                            record.setStatusText("approved");
                            break;
                        case 3:
                            record.setStatusText("rejected");
                            break;
                        case 4:
                            record.setStatusText("canceled");
                            break;
                        default:
                            record.setStatusText("unknown");
                    }
                }
            }
        }

        return pageResult != null ? Result.OK(pageResult) : Result.ERROR();
    }

    @Override
    public Result getPointsStatistics(Map<String, Object> params) {
        try {
            System.out.println("getPointsStatistics 原始参数: " + params);

            // 处理日期范围参数
            if (params != null && params.containsKey("dateRange")) {
                Object dateRange = params.get("dateRange");
                if (dateRange instanceof List && ((List<?>) dateRange).size() == 2) {
                    List<?> range = (List<?>) dateRange;
                    params.put("startDate", range.get(0));
                    params.put("endDate", range.get(1));
                }
                // 移除原始dateRange参数
                params.remove("dateRange");
            }

            System.out.println("getPointsStatistics 处理后参数: " + params);

            // 调用Mapper查询统计信息
            Map<String, Object> statistics = pointsApplyMapper.getPointsStatistics(params);

            System.out.println("getPointsStatistics 结果: " + statistics);

            // 如果没有数据，返回默认值
            if (statistics == null) {
                statistics = new HashMap<>();
                statistics.put("totalAddPoints", 0);
                statistics.put("totalDeductPoints", 0);
                statistics.put("pendingCount", 0);
                statistics.put("approvedCount", 0);
                statistics.put("rejectedCount", 0);
            }

            // 处理可能为null的值或类型转换
            Object addPoints = statistics.get("totalAddPoints");
            if (addPoints == null) {
                statistics.put("totalAddPoints", 0);
            } else if (addPoints instanceof Number) {
                statistics.put("totalAddPoints", ((Number)addPoints).intValue());
            }

            Object deductPoints = statistics.get("totalDeductPoints");
            if (deductPoints == null) {
                statistics.put("totalDeductPoints", 0);
            } else if (deductPoints instanceof Number) {
                statistics.put("totalDeductPoints", ((Number)deductPoints).intValue());
            }

            // 计算净积分 - 使用Number类型安全地获取值
            int totalAddPoints = statistics.get("totalAddPoints") instanceof Number ?
                    ((Number)statistics.get("totalAddPoints")).intValue() : 0;

            int totalDeductPoints = statistics.get("totalDeductPoints") instanceof Number ?
                    ((Number)statistics.get("totalDeductPoints")).intValue() : 0;

            statistics.put("netPoints", totalAddPoints - totalDeductPoints);

            return Result.OK(statistics);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.ERROR("获取积分统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 撤销积分申请
     * @param applyId 申请ID
     * @return Result
     */
    @Override
    public Result cancelPointsApplication(Integer applyId) {
        try {
            if (applyId == null) {
                return Result.ERROR("申请ID不能为空");
            }

            // 查询申请记录
            PointsApply pointsApply = pointsApplyMapper.selectById(applyId);
            if (pointsApply == null) {
                return Result.ERROR("申请记录不存在");
            }

            // 检查状态是否为待审核
            if (pointsApply.getStatus() != 1) {
                return Result.ERROR("只有待审核的申请才能撤销");
            }

            // 更新状态为已撤销(0)
            pointsApply.setStatus(0);
            pointsApply.setUpdateTime(new Date());
            pointsApply.setUpdateBy(UserContext.getCurrentUser().getUserId());

            int result = pointsApplyMapper.updateById(pointsApply);

            return result > 0 ? Result.OK("撤销成功") : Result.ERROR("撤销失败");
        } catch (Exception e) {
            e.printStackTrace();
            return Result.ERROR("撤销申请失败: " + e.getMessage());
        }
    }
    //-----------------------------------------------虚线一下全都是cmy写的--------------------------------------------------------------------
    @Override
    public Result queryTodayAddPoints() {
        QueryWrapper<PointsApply> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("points_change",1);
        queryWrapper.eq("status",2);
        queryWrapper.eq("status1",2);
        queryWrapper.eq("status2",2);
        //查询当天的一个加分记录
        queryWrapper.ge("create_time", LocalDate.now().atStartOfDay()).lt("create_time", LocalDate.now().plusDays(1).atStartOfDay());
        List<PointsApply> pointsApplies = pointsApplyMapper.selectList(queryWrapper);
        for (PointsApply pointsApply : pointsApplies) {
            String studentNo = pointsApply.getStudentNo();
            QueryWrapper<EduStudent> studentWrapper = new QueryWrapper<>();
            studentWrapper.eq("student_no", studentNo);
            EduStudent student = eduStudentService.getOne(studentWrapper);
            if (student != null) {
                pointsApply.setStudentName(student.getRealName());
            }
            pointsApply.setStudentName(student.getRealName());
        }
        return Result.OK(pointsApplies);
    }

    @Override
    public Result queryTodayMinusPoints() {
        QueryWrapper<PointsApply> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("points_change",2);
        queryWrapper.eq("status",2);
        queryWrapper.eq("status1",2);
        queryWrapper.eq("status2",2);
        //查询当天的一个加分记录
        queryWrapper.ge("create_time", LocalDate.now().atStartOfDay()).lt("create_time", LocalDate.now().plusDays(1).atStartOfDay());
        List<PointsApply> pointsApplies = pointsApplyMapper.selectList(queryWrapper);
        for (PointsApply pointsApply : pointsApplies) {
            String studentNo = pointsApply.getStudentNo();
            QueryWrapper<EduStudent> studentWrapper = new QueryWrapper<>();
            studentWrapper.eq("student_no", studentNo);
            EduStudent student = eduStudentService.getOne(studentWrapper);
            if (student != null) {
                pointsApply.setStudentName(student.getRealName());
            }
        }
        return Result.OK(pointsApplies);
    }

    /**
     * 根据当前用户角色自动设置初始审核状态
     * @param pointsApply 积分申请对象
     */
    private void setInitialApprovalStatus(PointsApply pointsApply) {
        try {
            SysUser currentUser = UserContext.getCurrentUser();
            if (currentUser == null) {
                // 如果无法获取当前用户，设置默认状态（全部待审核）
                pointsApply.setStatus(1);    // 导员讲师审核：待审核
                pointsApply.setStatus1(1);   // 主任审核：待审核
                pointsApply.setStatus2(1);   // 院长审核：待审核
                return;
            }

            Integer userId = currentUser.getUserId();

            // 查询用户的角色
            QueryWrapper<SysUserRole> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_id", userId);
            List<SysUserRole> userRoles = sysUserRoleMapper.selectList(queryWrapper);

            // 提取角色ID列表
            List<Integer> roleIds = userRoles.stream()
                    .map(SysUserRole::getRoleId)
                    .collect(Collectors.toList());

            System.out.println("当前用户角色ID列表: " + roleIds);

            // 初始化所有状态为待审核
            pointsApply.setStatus(1);    // 导员讲师审核：待审核
            pointsApply.setStatus1(1);   // 主任审核：待审核
            pointsApply.setStatus2(1);   // 院长审核：待审核

            // 根据用户角色自动通过相应的审核
            // 角色映射：1-超级管理员, 2-专业主任, 3-专病主任, 4-讲师, 5-导员, 6-秘书
            // 权限从高到低：1 > 2,3 > 4,5 > 6

            // 按权限从高到低判断，取最高权限角色
            if (roleIds.contains(1)) {
                // 超级管理员(1)拥有最高权限，自动通过所有审核
                pointsApply.setStatus(2);   // 自动通过导员讲师审核
                pointsApply.setStatus1(2);  // 自动通过主任审核
                pointsApply.setStatus2(1);  // 自动通过院长审核
                System.out.println("用户具有超级管理员角色，自动通过所有审核");
            } else if (roleIds.contains(2) || roleIds.contains(3)) {
                // 专业主任(2)或专病主任(3)，自动通过导员讲师审核和主任审核
                pointsApply.setStatus(2);   // 自动通过导员讲师审核
                pointsApply.setStatus1(2);  // 自动通过主任审核
                System.out.println("用户具有主任角色，自动通过导员讲师审核和主任审核");
            } else if (roleIds.contains(4) || roleIds.contains(5)) {
                // 讲师(4)或导员(5)，自动通过导员讲师审核
                pointsApply.setStatus(2);
                System.out.println("用户具有讲师/导员角色，自动通过导员讲师审核");
            } else if (roleIds.contains(6)) {
                // 秘书(6)，所有审核都需要等待
                System.out.println("用户具有秘书角色，所有审核都需要等待");
            }

            System.out.println("最终审核状态 - 导员讲师: " + pointsApply.getStatus() +
                    ", 主任: " + pointsApply.getStatus1() +
                    ", 院长: " + pointsApply.getStatus2());

        } catch (Exception e) {
            System.err.println("设置初始审核状态时出错: " + e.getMessage());
            e.printStackTrace();
            // 出错时设置默认状态
            pointsApply.setStatus(1);
            pointsApply.setStatus1(1);
            pointsApply.setStatus2(1);
        }
    }

    /**
     * 获取讲师/导员的积分历史记录（只能看到自己班级学生的申请和自己创建的申请）
     */
    private Result getPointsHistoryForTeacher(PointsApply pointsApply, Integer teacherId) {
        try {
            // 设置默认分页参数
            Integer pageNum = pointsApply.getPageNum();
            Integer pageSize = pointsApply.getPageSize();
            if (pageNum == null || pageNum < 1) {
                pageNum = 1;
            }
            if (pageSize == null || pageSize < 1) {
                pageSize = 10;
            }

            // 构建查询参数
            Map<String, Object> params = new HashMap<>();
            if (pointsApply.getStudentNo() != null) {
                params.put("studentNo", pointsApply.getStudentNo());
            }
            if (pointsApply.getPointsChange() != null) {
                params.put("pointsChange", pointsApply.getPointsChange());
            }
            if (pointsApply.getStatus() != null) {
                params.put("status", pointsApply.getStatus());
            }
            // 其他查询条件可以根据需要添加

            System.out.println("教师查询参数: " + params + ", 教师ID: " + teacherId);

            // 使用专门的教师查询方法
            List<Map<String, Object>> records = pointsApplyMapper.getPointsHistoryForTeacher(params, teacherId);

            System.out.println("教师查询结果数量: " + (records != null ? records.size() : 0));

            // 手动分页
            int total = records != null ? records.size() : 0;
            int startIndex = (pageNum - 1) * pageSize;
            int endIndex = Math.min(startIndex + pageSize, total);

            List<Map<String, Object>> pagedRecords = new ArrayList<>();
            if (records != null && startIndex < total) {
                pagedRecords = records.subList(startIndex, endIndex);
            }

            // 构建分页结果
            Map<String, Object> result = new HashMap<>();
            result.put("records", pagedRecords);
            result.put("total", total);
            result.put("size", pageSize);
            result.put("current", pageNum);
            result.put("pages", (total + pageSize - 1) / pageSize);

            return Result.OK(result);

        } catch (Exception e) {
            System.err.println("获取教师积分历史时出错: " + e.getMessage());
            e.printStackTrace();
            return Result.ERROR("获取积分历史失败: " + e.getMessage());
        }
    }

    /**
     * 获取秘书的积分历史记录（只能看到自己申请的记录）
     */
    private Result getPointsHistoryForSecretary(PointsApply pointsApply, Integer secretaryId) {
        try {
            // 设置默认分页参数
            Integer pageNum = pointsApply.getPageNum();
            Integer pageSize = pointsApply.getPageSize();
            if (pageNum == null || pageNum < 1) {
                pageNum = 1;
            }
            if (pageSize == null || pageSize < 1) {
                pageSize = 10;
            }

            System.out.println("秘书查询自己申请的记录，用户ID: " + secretaryId);

            // 使用专门的秘书查询方法
            List<Map<String, Object>> records = pointsApplyMapper.getPointsHistoryByApplyUser(secretaryId);

            System.out.println("秘书查询结果数量: " + (records != null ? records.size() : 0));

            // 手动分页
            int total = records != null ? records.size() : 0;
            int startIndex = (pageNum - 1) * pageSize;
            int endIndex = Math.min(startIndex + pageSize, total);

            List<Map<String, Object>> pagedRecords = new ArrayList<>();
            if (records != null && startIndex < total) {
                pagedRecords = records.subList(startIndex, endIndex);
            }

            // 构建分页结果
            Map<String, Object> result = new HashMap<>();
            result.put("records", pagedRecords);
            result.put("total", total);
            result.put("size", pageSize);
            result.put("current", pageNum);
            result.put("pages", (total + pageSize - 1) / pageSize);

            return Result.OK(result);

        } catch (Exception e) {
            System.err.println("获取秘书积分历史时出错: " + e.getMessage());
            e.printStackTrace();
            return Result.ERROR("获取积分历史失败: " + e.getMessage());
        }
    }
}
