package com.zhentao.pojo;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 积分变动记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@Getter
@Setter
@TableName("points_record")
public class PointsRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 记录ID
     */
    @TableId(value = "record_id", type = IdType.AUTO)
    private Long recordId;

    /**
     * 记录编号
     */
    private String recordNo;
    @TableField(exist = false)
    private String studentName;

    /**
     * 学生ID
     */
    private String studentNo;

    /**
     * 变动前积分
     */
    private Integer pointsBefore;

    /**
     * 积分变动值（正数为加分，负数为减分）
     */
    private Integer pointsChange;

    /**
     * 变动后积分
     */
    private Integer pointsAfter;

    /**
     * 关联的申请ID
     */
    private Integer applyId;

    /**
     * 操作人ID
     */
    private Integer operatorId;

    /**
     * 操作类型（1-申请加分，2-申请减分，3-审核通过，4-审核拒绝，5-系统调整）
     */
    private Integer operationType;

    /**
     * 操作时间
     */
    private Date operationTime;

    /**
     * 变动原因
     */
    private String reason;

    /**
     * 创建者ID
     */
    private Integer createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新者ID
     */
    private Integer updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标志（0-存在，1-删除）
     */
    private Integer delFlag;
}
