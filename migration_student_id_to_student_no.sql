-- 数据库迁移脚本：将 points_apply 表的 student_id 字段从存储ID改为存储学号
-- 执行前请备份数据库！

-- 方案1：直接修改现有 student_id 字段的数据类型和内容（推荐）
-- 这种方案不需要添加新字段，直接将现有的 student_id 字段改为存储学号

-- 1. 首先备份现有数据（可选，但强烈推荐）
-- CREATE TABLE points_apply_backup AS SELECT * FROM points_apply;

-- 2. 添加临时字段存储学号
ALTER TABLE points_apply ADD COLUMN temp_student_no VARCHAR(50);

-- 3. 将学生ID转换为学号并存储到临时字段
UPDATE points_apply pa
INNER JOIN edu_student es ON pa.student_id = es.student_id
SET pa.temp_student_no = es.student_no;

-- 4. 检查数据迁移是否完整
SELECT
    COUNT(*) as total_records,
    COUNT(temp_student_no) as migrated_records,
    COUNT(*) - COUNT(temp_student_no) as failed_records
FROM points_apply;

-- 5. 如果迁移完整，修改字段类型并复制数据
-- 注意：这会删除原有的 student_id 数据！
ALTER TABLE points_apply MODIFY COLUMN student_id VARCHAR(50) COMMENT '学生学号';
UPDATE points_apply SET student_id = temp_student_no;

-- 6. 删除临时字段
ALTER TABLE points_apply DROP COLUMN temp_student_no;

-- 7. 为字段添加索引以提高查询性能
CREATE INDEX idx_points_apply_student_id ON points_apply(student_id);

-- 8. 同样需要修改 points_record 表（如果存在相同问题）
-- ALTER TABLE points_record ADD COLUMN temp_student_no VARCHAR(50);
-- UPDATE points_record pr
-- INNER JOIN edu_student es ON pr.student_id = es.student_id
-- SET pr.temp_student_no = es.student_no;
-- ALTER TABLE points_record MODIFY COLUMN student_id VARCHAR(50) COMMENT '学生学号';
-- UPDATE points_record SET student_id = temp_student_no;
-- ALTER TABLE points_record DROP COLUMN temp_student_no;
-- CREATE INDEX idx_points_record_student_id ON points_record(student_id);
