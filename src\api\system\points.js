import Request from "@/request/index.js";

export function findAll<PERSON>ia(data){
    return Request({
        url: '/integral-rules/findAllJia',
        method: 'post',
        data:data
    })
}
export function findAllJian(data){
    return Request({
        url: '/integral-rules/findAllJian',
        method: 'post',
        data:data
    })
}
export function addRules(data){
    return Request({
        url: '/integral-rules/addRules',
        method: 'post',
        data:data
    })
}
export function updateRules(data){
    return Request({
        url: '/integral-rules/updateRules',
        method: 'post',
        data:data
    })
}
export function delRules(id){
    return Request({
        url: '/integral-rules/delRules?id='+id,
        method: 'post'
    })
}
export function findById(id){
    return Request({
        url: '/integral-rules/findById?id='+id,
        method: 'post'
    })
}
// 获取用户角色ID数组
const getRoleIds = () => {
    const roleIdStr = localStorage.getItem('roleId');
    console.log('从localStorage获取的roleId:', roleIdStr);
    if (!roleIdStr) return [];

    try {
        // 如果是数组格式的字符串，解析为数组
        if (roleIdStr.startsWith('[') && roleIdStr.endsWith(']')) {
            return JSON.parse(roleIdStr);
        }
        // 如果是逗号分隔的字符串，分割为数组
        if (roleIdStr.includes(',')) {
            return roleIdStr.split(',').map(id => parseInt(id.trim()));
        }
        // 如果是单个数字，转换为数组
        return [parseInt(roleIdStr)];
    } catch (e) {
        console.error('解析roleId失败:', e);
        return [];
    }
};

export function addJiFen(data){
    console.log('Sending data to backend:', data);

    // 获取用户角色ID数组
    const roleIds = getRoleIds();
    console.log('当前用户角色ID数组:', roleIds);

    // 根据角色设置审核状态
    // 角色映射：1-超级管理员, 2-专业主任, 3-专病主任, 4-讲师, 5-导员, 6-秘书
    // 权限从高到低：1 > 2,3 > 4,5 > 6
    let status=1;   // 导员讲师审核：默认待审核
    let status1=1;  // 主任审核：默认待审核
    let status2=1;  // 院长审核：默认待审核

    // 按权限从高到低判断，取最高权限角色
    if(roleIds.includes(1)){
        // 超级管理员(1)拥有最高权限，自动通过所有审核
        status=2;   // 自动通过导员讲师审核
        status1=2;  // 自动通过主任审核
        status2=1;  // 自动通过院长审核
        console.log('用户具有超级管理员角色，自动通过所有审核');
    } else if(roleIds.includes(2) || roleIds.includes(3)){
        // 专业主任(2)或专病主任(3)，自动通过导员讲师审核和主任审核
        status=2;   // 自动通过导员讲师审核
        status1=2;  // 自动通过主任审核
        console.log('用户具有主任角色，自动通过导员讲师审核和主任审核');
    } else if(roleIds.includes(4) || roleIds.includes(5)){
        // 讲师(4)或导员(5)，自动通过导员讲师审核
        status=2; // 自动通过导员讲师审核
        console.log('用户具有讲师/导员角色，自动通过导员讲师审核');
    } else if(roleIds.includes(6)){
        // 秘书(6)，所有审核都需要等待
        console.log('用户具有秘书角色，所有审核都需要等待');
    }

    console.log('设置的审核状态 - 导员讲师:', status, ', 主任:', status1, ', 院长:', status2);
    const requestData = {
        studentId: data.studentId || 0,
        classId: data.classId || 0,
        pointsChange: 1, // 1为加分，2为减分
        points: data.points || 0,
        reason: data.reason || '',
        img: data.img || '', // MinIO上传后的图片URL，多个URL用逗号分隔
        status: status, // 设置为待审核状态 (1-待审核，2-已通过，3-已拒绝)
        status1: status1, // 设置为待审核状态 (1-待审核，2-已通过，3-已拒绝)
        status2: status2 // 设置为待审核状态 (1-待审核，2-已通过，3-已拒绝)
    };

    console.log('Formatted request data:', requestData);

    // Use the real backend API
    return Request({
        url: '/points-apply/addJiFen',
        method: 'post',
        data: requestData
    })
}

export function searchStudents(keyword){
    // Use the real backend API instead of mock data
    return Request({
        url: '/edu-student/search',
        method: 'get',
        params: { keyword }
    })
}

export function getPointsRecords(data){
    console.log('Sending query to backend:', data);

    // Ensure data is an object
    const queryParams = data || {};

    return Request({
        url: '/points-apply/list',
        method: 'post',
        data: queryParams
    })
}

export function deductPoints(data){
    console.log('Sending deduction data to backend:', data);

    // 获取用户角色ID数组
    const roleIds = getRoleIds();
    console.log('当前用户角色ID数组:', roleIds);

    // 根据角色设置审核状态，与addJiFen保持一致
    // 角色映射：1-超级管理员, 2-专业主任, 3-专病主任, 4-讲师, 5-导员, 6-秘书
    // 权限从高到低：1 > 2,3 > 4,5 > 6
    let status=1;   // 导员讲师审核：默认待审核
    let status1=1;  // 主任审核：默认待审核
    let status2=1;  // 院长审核：默认待审核

    // 按权限从高到低判断，取最高权限角色
    if(roleIds.includes(1)){
        // 超级管理员(1)拥有最高权限，自动通过所有审核
        status=2;   // 自动通过导员讲师审核
        status1=2;  // 自动通过主任审核
        status2=2;  // 自动通过院长审核
        console.log('用户具有超级管理员角色，自动通过所有审核');
    } else if(roleIds.includes(2) || roleIds.includes(3)){
        // 专业主任(2)或专病主任(3)，自动通过导员讲师审核和主任审核
        status=2;   // 自动通过导员讲师审核
        status1=2;  // 自动通过主任审核
        console.log('用户具有主任角色，自动通过导员讲师审核和主任审核');
    } else if(roleIds.includes(4) || roleIds.includes(5)){
        // 讲师(4)或导员(5)，自动通过导员讲师审核
        status=2; // 自动通过导员讲师审核
        console.log('用户具有讲师/导员角色，自动通过导员讲师审核');
    } else if(roleIds.includes(6)){
        // 秘书(6)，所有审核都需要等待
        console.log('用户具有秘书角色，所有审核都需要等待');
    }

    console.log('设置的审核状态 - 导员讲师:', status, ', 主任:', status1, ', 院长:', status2);

    // 确保数据格式符合后端要求
    const requestData = {
        studentId: data.studentId || 0,
        classId: data.classId || 0,
        pointsChange: 2, // 2为减分
        points: data.points || 0,
        reason: data.reason || '',
        img: data.img || '', // MinIO上传后的图片URL，多个URL用逗号分隔
        status: status, // 根据角色设置状态
        status1: status1, // 根据角色设置状态
        status2: status2 // 根据角色设置状态
    };

    console.log('Formatted deduction request data:', requestData);

    // Use the same API endpoint as addJiFen but with pointsChange=2
    return Request({
        url: '/points-apply/addJiFen',
        method: 'post',
        data: requestData
    })
}

/**
 * 获取积分历史记录
 * @param {Object} params 查询参数
 * @returns {Promise} 请求Promise
 */
export function getPointsHistory(params) {
    // 确保params是一个对象
    const queryParams = params || {};

    // 确保分页参数存在
    if (!queryParams.pageNum) {
        queryParams.pageNum = 1;
    }
    if (!queryParams.pageSize) {
        queryParams.pageSize = 10;
    }

    console.log('发送历史记录查询参数:', queryParams);

    return Request({
        url: '/points-apply/history',
        method: 'post',
        data: queryParams
    })
}

/**
 * 获取积分统计信息
 * @param {Object} params 查询参数
 * @returns {Promise} 请求Promise
 */
export function getPointsStatistics(params) {
    console.log('Sending statistics query to backend:', params);

    // 确保params是一个对象
    const queryParams = params || {};

    // 处理日期范围
    if (queryParams.startTime && typeof queryParams.startTime === 'object') {
        queryParams.startTime = formatDate(queryParams.startTime);
    }

    if (queryParams.endTime && typeof queryParams.endTime === 'object') {
        queryParams.endTime = formatDate(queryParams.endTime);
    }

    return Request({
        url: '/points-apply/statistics',
        method: 'post',
        data: queryParams
    })
}

/**
 * 格式化日期为字符串 (YYYY-MM-DD)
 * @param {Date} date 日期对象
 * @returns {string} 格式化后的日期字符串
 */
function formatDate(date) {
    if (!date) return '';
    if (typeof date === 'string') return date;

    try {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');

        return `${year}-${month}-${day}`;
    } catch (e) {
        console.error('Date formatting error:', e);
        return '';
    }
}

/**
 * 撤销积分申请
 * @param {Number} applyId 申请ID
 * @returns {Promise} 请求Promise
 */
export function cancelPointsApplication(applyId) {
    console.log('Canceling points application:', applyId);

    return Request({
        url: '/points-apply/cancel',
        method: 'post',
        data: { applyId }
    })
}

//---------------------------------------虚线一下全是cmy写的----------------------------------------------------------
/**
 * 查询当天所有加分的学生
 */
export function queryTodayAddPoints() {
  return Request({
    url: '/points-apply/queryTodayAddPoints',
    method: 'post'
  });
}

/**
 * 查询当天所有减分的学生
 */
export function queryTodayMinusPoints() {
  return Request({
    url: '/points-apply/queryTodayMinusPoints',
    method: 'post'
  });
}