package com.zhentao.pojo;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 积分变动申请表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@Getter
@Setter
@TableName("points_apply")
public class PointsApply implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 申请ID
     */
    @TableId(value = "apply_id", type = IdType.AUTO)
    private Integer applyId;

    /**
     * 学生学号（存储在student_id字段中，但现在存储学号而不是ID）
     */
    @TableField("student_id")
    private String studentNo;
    @TableField(exist = false)
    private EduStudent student;

    @TableField(exist = false)
    private Integer pageSize;
    @TableField(exist = false)
    private Integer pageNum;
    @TableField(exist = false)
    private Date startTime;
    @TableField(exist = false)
    private Date endTime;
    @TableField(exist = false)
    private String studentName;
    @TableField(exist = false)
    private String className;
    @TableField(exist = false)
    private String type;
    @TableField(exist = false)
    private String statusText;

    /**
     * 申请人ID
     */
    private Integer applyUserId;
    @TableField(exist = false)
    private SysUser applyUser;

    /**
     * 积分变动值（正数为加分，负数为减分）
     */
    private Integer pointsChange;

    /**
     * 申请理由
     */
    private String reason;

    /**
     * 证明图片URL，多个以逗号分隔
     */
    private String evidenceImages;

    /**
     * 导员讲师审核 状态（1-待审核，2-已通过，3-已拒绝）
     */
    private Integer status;
    /**
     * 主任审核 状态（1-待审核，2-已通过，3-已拒绝）
     */
    private Integer status1;
    /**
     * 院长审核 状态（1-待审核，2-已通过，3-已拒绝）
     */
    private Integer status2;

    /**
     * 审核人ID
     */
    private Integer reviewerId;
    @TableField(exist = false)
    private SysUser reviewer;

    /**
     * 审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date reviewTime;

    /**
     * 审核意见
     */
    private String reviewComment;

    @TableField(exist = false)
    private String img;
    /**
     * 创建者ID
     */
    private Integer createBy;
    @TableField(exist = false)
    private SysUser createUser;
    private Integer classId;
    @TableField(exist = false)
    private EduClass eduClass;
    private Integer points;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新者ID
     */
    private Integer updateBy;
    @TableField(exist = false)
    private SysUser updateUser;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date updateTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标志（0-存在，1-删除）
     */
    private String delFlag;
}
